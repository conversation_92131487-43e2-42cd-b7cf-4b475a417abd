<template>
	<div class="flex flex-nowrap overflow-x-scroll no-scrollbar md:block md:overflow-auto px-6 py-8 border-b md:border-b-0 md:border-r border-gray-100 min-w-72 md:space-y-6 bg-gray-50/50">
		<!-- Account Settings Group -->
		<div v-if="isAdmin" class="mb-8">
			<div class="text-xs font-semibold text-gray-500 uppercase mb-4 tracking-wider">Account Settings</div>
			<ul class="flex flex-nowrap md:block mr-3 md:mr-0 space-y-1">
				<router-link to="/loyalty/settings/manage-users" custom v-slot="{href, navigate, isExactActive}">
					<li class="mr-0.5 md:mr-0">
						<a class="flex items-center px-4 py-3 rounded-xl whitespace-nowrap transition-all duration-200 group hover:bg-white hover:shadow-sm"
							:class="isExactActive ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900'"
							:href="href" @click="navigate">
							<svg class="w-5 h-5 shrink-0 fill-current mr-3 transition-colors duration-200"
								:class="isExactActive ? 'text-white' : 'text-gray-400 group-hover:text-purple-600'" viewBox="0 0 20 20">
								<path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
							</svg>
							<span class="text-sm font-medium">Users</span>
						</a>
					</li>
				</router-link>
				<router-link to="/loyalty/settings/data" custom v-slot="{href, navigate, isExactActive}">
					<li class="mr-0.5 md:mr-0">
						<a class="flex items-center px-4 py-3 rounded-xl whitespace-nowrap transition-all duration-200 group hover:bg-white hover:shadow-sm"
							:class="isExactActive ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900'"
							:href="href" @click="navigate">
							<svg class="w-5 h-5 shrink-0 fill-current mr-3 transition-colors duration-200"
								:class="isExactActive ? 'text-white' : 'text-gray-400 group-hover:text-purple-600'" viewBox="0 0 20 20">
								<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
							</svg>
							<span class="text-sm font-medium">Data</span>
						</a>
					</li>
				</router-link>
			</ul>
		</div>

		<!-- Loyalty Settings Group -->
		<div v-if="isAdmin && !shouldHideLoyaltySettings" class="mb-8">
			<div class="text-xs font-semibold text-gray-500 uppercase mb-4 tracking-wider">Loyalty Settings</div>
			<ul class="flex flex-nowrap md:block mr-3 md:mr-0 space-y-1">
				<router-link to="/settings/loyalty" custom v-slot="{href, navigate, isExactActive}">
					<li class="mr-0.5 md:mr-0">
						<a class="flex items-center px-4 py-3 rounded-xl whitespace-nowrap transition-all duration-200 group hover:bg-white hover:shadow-sm"
							:class="isExactActive ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900'"
							:href="href" @click="navigate">
							<svg class="w-5 h-5 shrink-0 fill-current mr-3 transition-colors duration-200"
								:class="isExactActive ? 'text-white' : 'text-gray-400 group-hover:text-purple-600'" viewBox="0 0 20 20">
								<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
							</svg>
							<span class="text-sm font-medium">Programs</span>
						</a>
					</li>
				</router-link>
				<router-link to="/loyalty/settings/manage-currencies" custom v-slot="{href, navigate, isExactActive}">
					<li class="mr-0.5 md:mr-0">
						<a class="flex items-center px-4 py-3 rounded-xl whitespace-nowrap transition-all duration-200 group hover:bg-white hover:shadow-sm"
							:class="isExactActive ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900'"
							:href="href" @click="navigate">
							<svg class="w-5 h-5 shrink-0 fill-current mr-3 transition-colors duration-200"
								:class="isExactActive ? 'text-white' : 'text-gray-400 group-hover:text-purple-600'" viewBox="0 0 20 20">
								<path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
							</svg>
							<span class="text-sm font-medium">Currency & Language</span>
						</a>
					</li>
				</router-link>
				<router-link to="/loyalty/settings/translations" custom v-slot="{href, navigate, isExactActive}">
					<li class="mr-0.5 md:mr-0">
						<a class="flex items-center px-4 py-3 rounded-xl whitespace-nowrap transition-all duration-200 group hover:bg-white hover:shadow-sm"
							:class="isExactActive ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900'"
							:href="href" @click="navigate">
							<svg class="w-5 h-5 shrink-0 fill-current mr-3 transition-colors duration-200"
								:class="isExactActive ? 'text-white' : 'text-gray-400 group-hover:text-purple-600'" viewBox="0 0 20 20">
								<path d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM6 4a1 1 0 011-1h6a1 1 0 011 1v12a1 1 0 01-1 1H7a1 1 0 01-1-1V4zm2 2a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zm0 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zm0 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>
							</svg>
							<span class="text-sm font-medium">Translations</span>
						</a>
					</li>
				</router-link>
				<router-link to="/loyalty/settings/shopify-extensions" custom v-slot="{href, navigate, isExactActive}">
					<li class="mr-0.5 md:mr-0">
						<a class="flex items-center px-4 py-3 rounded-xl whitespace-nowrap transition-all duration-200 group hover:bg-white hover:shadow-sm"
							:class="isExactActive ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900'"
							:href="href" @click="navigate">
							<svg class="w-5 h-5 shrink-0 fill-current mr-3 transition-colors duration-200"
								:class="isExactActive ? 'text-white' : 'text-gray-400 group-hover:text-purple-600'" viewBox="0 0 20 20">
								<path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM4 8a1 1 0 000 2h1a1 1 0 100-2H4zm0 4a1 1 0 100 2h1a1 1 0 100-2H4zm4-4a1 1 0 000 2h1a1 1 0 100-2H8zm0 4a1 1 0 100 2h1a1 1 0 100-2H8zm4-4a1 1 0 000 2h1a1 1 0 100-2h-1zm0 4a1 1 0 100 2h1a1 1 0 100-2h-1z"/>
							</svg>
							<span class="text-sm font-medium">Extensions</span>
						</a>
					</li>
				</router-link>
			</ul>
		</div>

		<!-- User Settings Group -->
		<div>
			<div class="text-xs font-semibold text-gray-500 uppercase mb-4 tracking-wider">User Settings</div>
			<ul class="flex flex-nowrap md:block mr-3 md:mr-0 space-y-1">
				<router-link to="/loyalty/settings/myprofile" custom v-slot="{href, navigate, isExactActive}">
					<li class="mr-0.5 md:mr-0">
						<a class="flex items-center px-4 py-3 rounded-xl whitespace-nowrap transition-all duration-200 group hover:bg-white hover:shadow-sm"
							:class="isExactActive ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900'"
							:href="href" @click="navigate">
							<svg class="w-5 h-5 shrink-0 fill-current mr-3 transition-colors duration-200"
								:class="isExactActive ? 'text-white' : 'text-gray-400 group-hover:text-purple-600'" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
							</svg>
							<span class="text-sm font-medium">My Profile</span>
						</a>
					</li>
				</router-link>
			</ul>
		</div>
	</div>
</template>

<script>
import { PLAN_IDS } from '../../utils/Utils';

export default {
	name: 'SettingsSidebar',
	computed: {
		isAdmin() {
			let userInfo = JSON.parse(localStorage.getItem('userInfo'));
			return userInfo && userInfo.roles.includes('admin') || userInfo.roles.includes('customer-admin');
		},
		shouldHideLoyaltySettings() {
			try {
				const featureStates = JSON.parse(localStorage.getItem('featureStates') || '{}');

				// Check both current org plan and parent org plan
				const primaryPlanId = featureStates?.plan?.id;
				const currentOrgPlanId = featureStates?.currentOrgPlan?.id;
				const parentOrgPlanId = featureStates?.parentOrgPlan?.id;

				// Helper function to check if a plan ID is a Strategist plan
				const isStrategistPlan = (planId) => {
					return planId && (planId >= PLAN_IDS.STRATEGIST_START && planId <= PLAN_IDS.STRATEGIST_END);
				};

				// Hide Loyalty Settings if ANY of the plans is a Strategist plan
				return isStrategistPlan(primaryPlanId) ||
				       isStrategistPlan(currentOrgPlanId) ||
				       isStrategistPlan(parentOrgPlanId);
			} catch (error) {
				console.error('Error checking plan for Loyalty Settings visibility:', error);
				return false;
			}
		}
	}
}
</script>
