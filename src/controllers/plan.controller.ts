import {inject, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {OrganizationPlanRepository, OrganizationRepository, PlanRepository} from '../repositories';
import {
	post,
	requestBody,
	api,
	get,
	getModelSchemaRef,
	del,
	param
} from '@loopback/rest';

import {guardStrategy, skipGuardCheck, injectUserOrgId, OrgGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
import {OrganizationPlan, Plan, Organization} from '../models';
import {BillingService} from '../services/shopify/billing.service';
import {StripeBillingService} from '../services/stripe/stripe-billing.service';
import e from 'express';
import {FeatureService} from '../services/feature.service';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<OrganizationPlan>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: OrganizationPlanRepository
}))
export class PlanController {
	constructor(
		@repository(PlanRepository) protected planRepository: PlanRepository,
		@repository(OrganizationPlanRepository) protected orgPlanRepository: OrganizationPlanRepository,
		@repository(OrganizationRepository) protected orgRepository: OrganizationRepository,
		@service(BillingService) private billingService: BillingService,
		@service(StripeBillingService) private stripeBillingService: StripeBillingService,
		@service(FeatureService) private featureService: FeatureService
	) { }

	@post('/create-subscription', {
		responses: {
			'200': {
				description: 'Confirmation URL',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								confirmationUrl: { type: 'string' }
							}
						}
					}
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async createSubscription(
		@requestBody({
			content: {
			  'application/json': {
				schema: {
				  type: 'object',
				  properties: {
					planId: { type: 'number' },
					revenue: { type: 'number' }
				  },
				  required: ['planId'],
				},
			  },
			},
		  }) body: { planId: number, revenue: number },
		@injectUserOrgId() orgId: number,
	) {
		console.log(`Creating subscription for plan: ${body.planId} for org: ${orgId}`);

		// Find the plan details
		let plan = await this.planRepository.findOne({
			where: {
				id: body.planId
			},
			include: ['planRevenuePricings']
		});

		if (!plan) {
			throw new Error(`Plan with ID ${body.planId} not found`);
		}

		// Determine the price (handle revenue-based pricing if applicable)
		let price = plan.price;
		if (plan.hasRevenueBasedPricing && body.revenue) {
			console.log(`Revenue based pricing for plan: ${body.planId} for org: ${orgId}, with specified revenue ${body.revenue}`);
			let pricing = plan.planRevenuePricings.find((pricing) => {
				return pricing.revenueUsdMin <= body.revenue && (pricing.revenueUsdMax == null || pricing.revenueUsdMax >= body.revenue);
			});
			if (pricing) {
				console.log(`Found matching revenue-based pricing: ${JSON.stringify(pricing)}`);
				price = pricing.price;
			}
		}

		// Update other PENDING plans to CANCELLED
		await this.orgPlanRepository.updateAll({
			status: 'CANCELLED',
			subscriptionId: undefined,
			confirmationUrl: undefined
		}, {
			orgId: orgId,
			status: 'PENDING',
			planId: { neq: body.planId }
		});

		// 2. Find the specific plan we're interested in
		let orgPlan = await this.orgPlanRepository.findOne({
			where: {
				orgId: orgId,
				planId: body.planId
			}
		});

		// 3. Get all existing org plans to track and manage
		const allOrgPlans = await this.orgPlanRepository.find({
			where: {
				orgId: orgId
			}
		});


		// Check for any price override for this org+plan combination
		if (orgPlan && orgPlan.planId === body.planId) {
			price = orgPlan.priceOverride != null ? orgPlan.priceOverride : price;
		}

		let responseUrl: string = '';

		// CASE 1: FREE PLAN REQUESTED (planId === 1)
		if (body.planId === 1) {
			console.log(`Free plan requested for org: ${orgId}. Canceling any existing subscriptions.`);

			// Cancel all active Stripe subscriptions
			await this.stripeBillingService.cleanupSubscriptions(orgId);

			// Mark all org plans as CANCELLED except the free plan and clear scheduled dates
			for (const plan of allOrgPlans) {
				if ((plan.status === 'ACTIVE' || plan.status === 'PENDING') && plan.planId !== 1) {
					await this.orgPlanRepository.updateById(plan.id, {
						status: 'CANCELLED',
						subscriptionId: undefined,
						confirmationUrl: undefined,
						scheduledEnd: undefined,
						scheduledStart: undefined
					});
				}
			}

			// Set free plan to ACTIVE
			if (orgPlan) {
				// Update existing free plan to ACTIVE and clear scheduled dates
				await this.orgPlanRepository.updateById(orgPlan.id, {
					status: 'ACTIVE',
					subscriptionId: undefined,
					confirmationUrl: undefined,
					scheduledEnd: undefined,
					scheduledStart: undefined
				});
			} else {
				// Create new free plan record - handle the typescript type issue
				const newOrgPlan = await this.orgPlanRepository.create({
					orgId: orgId,
					planId: body.planId,
					status: 'ACTIVE',
					subscriptionId: undefined,
					confirmationUrl: undefined,
					scheduledEnd: undefined,
					scheduledStart: undefined
				});
				console.log(`Created new free plan record with ID: ${newOrgPlan.id}`);
			}

			// Update features immediately for free plan
			this.featureService.updateLoyaltyAndGwpEnabled(orgId)
				.catch((err) => console.error('Error updating features for free plan:', err));

			responseUrl = '/loyalty/settings/plans';
		}
		// CASE 2: PAID PLAN REQUESTED
		else {
			console.log(`Paid plan requested for org: ${orgId}, planId: ${body.planId}, price: $${price}`);

			// Check if there's an existing active Shopify subscription that needs to be cancelled first
			const existingActiveShopifyPlan = allOrgPlans.find(plan =>
				plan.status === 'ACTIVE' &&
				plan.subscriptionId &&
				plan.subscriptionId.startsWith('gid://shopify/')
			);

			if (existingActiveShopifyPlan && existingActiveShopifyPlan.subscriptionId) {
				console.log(`Found existing active Shopify subscription ${existingActiveShopifyPlan.subscriptionId}, cancelling before creating new plan`);

				try {
					// Cancel the existing Shopify subscription
					const shopifyResult = await this.billingService.unsubscribe(
						orgId,
						existingActiveShopifyPlan.subscriptionId
					);

					console.log(`Shopify unsubscribe result: ${JSON.stringify(shopifyResult)}`);

					if(shopifyResult.data.appSubscriptionCancel.appSubscription.status == 'CANCELLED') {
						// Mark the old plan as cancelled
						await this.orgPlanRepository.updateById(existingActiveShopifyPlan.id, {
							status: 'CANCELLED',
							priceOverride: undefined,
							confirmationUrl: undefined,
							subscriptionId: undefined,
							scheduledEnd: undefined,
							scheduledStart: undefined
						});

						console.log(`Successfully cancelled existing Shopify subscription for plan ${existingActiveShopifyPlan.id}`);
					}
				} catch (error) {
					console.error('Error cancelling existing Shopify subscription:', error);
					// Continue with plan creation even if cancellation fails
				}
			}

			// Find or create the organization plan record for this specific plan
			try {
				const result = await this.stripeBillingService.migrateToPlan(orgId, plan, price);

				responseUrl = result?.confirmationUrl!;
			} catch (error) {
				console.error('Error processing Stripe subscription:', error);
				throw new Error(`Failed to process subscription: ${error.message}`);
			}
		}

		// Return the confirmation URL
		return { confirmationUrl: responseUrl || '/loyalty/settings/plans' };
	}

	@del('/feature-cache', {
		responses: {
			'200': {
				description: 'Status',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								status: { type: 'string' }
							}
						}
					}
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async clearFeatureCache(
		@param.query.number('orgId') orgId: number,
	) {
		// this.featureService.invalidateCache(orgId);
	}


	@post('/update-feature-state', {
		responses: {
			'200': {
				description: 'Status',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								status: { type: 'string' }
							}
						}
					}
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async updateFeatureState(
		@param.query.number('orgId') orgId: number,
	) {
		// this.featureService.invalidateCache(orgId);
		return this.featureService.updateLoyaltyAndGwpEnabled(orgId);
	}

	@post('/update-free-trials')
	@skipGuardCheck()
	async updateFreeTrials() {
		// Handle free trial expirations
		const freeTrialPlans = await this.orgPlanRepository.find({
			where: {
				planId: 4,
				subscriptionId: null as any,
				status: { neq: 'TRIAL_EXPIRED' }
			},
			include: ['organization']
		});

		const expiredPlans = freeTrialPlans.filter((plan) => {
			const startDate = new Date(plan.organization.createdDate!);
			// Agency organizations get 30 days, everyone else gets 14 days
			const trialLengthDays = plan.organization.orgType === 'agency' ? 30 : 14;

			const date = new Date();
			const endDate = new Date(startDate);
			endDate.setDate(startDate.getDate() + trialLengthDays);

			return date > endDate;
		});

		await this.orgPlanRepository.updateAll({
			status: 'TRIAL_EXPIRED'
		}, {
			id: { inq: expiredPlans.map((plan) => plan.id) }
		});

		for (let plan of expiredPlans) {
			await this.featureService.updateLoyaltyAndGwpEnabled(plan.orgId);
		}

		// Handle scheduled plan transitions (downgrades)
		const currentDate = new Date();

		// Find plans that are scheduled to end
		const plansToEnd = await this.orgPlanRepository.find({
			where: {
				status: 'ACTIVE',
				scheduledEnd: { lte: currentDate.toISOString() }
			}
		});

		// Find plans that are scheduled to start
		const plansToStart = await this.orgPlanRepository.find({
			where: {
				status: 'PENDING_NEXT_RENEWAL',
				scheduledStart: { lte: currentDate.toISOString() }
			}
		});

		console.log(`Found ${plansToEnd.length} plans to end and ${plansToStart.length} plans to start`);

		// Process scheduled plan transitions
		for (const planToEnd of plansToEnd) {
			// Check if this is a scheduled cancellation (no corresponding plan to start)
			const correspondingPlanToStart = plansToStart.find(p => p.orgId === planToEnd.orgId);

			if (!correspondingPlanToStart) {
				// This is a scheduled cancellation - cancel the Stripe subscription and mark as cancelled
				console.log(`Processing scheduled cancellation for plan ${planToEnd.id} for org ${planToEnd.orgId}`);

				if (planToEnd.subscriptionId) {
					try {
						// Cancel the subscription in Stripe
						await this.stripeBillingService.unsubscribe(planToEnd.orgId, planToEnd.subscriptionId);
						console.log(`Cancelled Stripe subscription ${planToEnd.subscriptionId} for scheduled cancellation`);
					} catch (error) {
						console.error(`Error cancelling Stripe subscription ${planToEnd.subscriptionId} for scheduled cancellation:`, error);
					}
				}
			}

			// Mark the old plan as cancelled and clear scheduled dates
			await this.orgPlanRepository.updateById(planToEnd.id, {
				status: 'CANCELLED',
				subscriptionId: undefined,
				confirmationUrl: undefined,
				scheduledEnd: undefined,
				scheduledStart: undefined
			});

			console.log(`Ended scheduled plan ${planToEnd.id} for org ${planToEnd.orgId}`);

			// Update features for this organization
			await this.featureService.updateLoyaltyAndGwpEnabled(planToEnd.orgId);
		}

		for (const planToStart of plansToStart) {
			// Activate the new plan and clear all scheduled dates
			await this.orgPlanRepository.updateById(planToStart.id, {
				status: 'ACTIVE',
				scheduledStart: undefined, // Clear the scheduled start date
				scheduledEnd: undefined // Clear any scheduled end date
			});

			console.log(`Activated scheduled plan ${planToStart.id} for org ${planToStart.orgId}`);

			// Update features for this organization
			await this.featureService.updateLoyaltyAndGwpEnabled(planToStart.orgId);

			// Update the Stripe subscription to the new plan/price
			if (planToStart.subscriptionId) {
				try {
					// Get the plan details to update Stripe
					const plan = await this.planRepository.findById(planToStart.planId);
					if (plan) {
						const finalAmount = planToStart.priceOverride || plan.price;

						// Now update Stripe to the new price (this will be at the billing cycle, so no proration)
						await this.stripeBillingService.updateSubscriptionAtBillingCycle(
							planToStart.orgId,
							planToStart.subscriptionId,
							plan.name,
							finalAmount
						);

						console.log(`Updated Stripe subscription ${planToStart.subscriptionId} for org ${planToStart.orgId} to new plan ${plan.name}`);
					}
				} catch (error) {
					console.error(`Error updating Stripe subscription for plan ${planToStart.id}:`, error);
				}
			}
		}

		return {
			expiredTrials: expiredPlans.length,
			endedPlans: plansToEnd.length,
			startedPlans: plansToStart.length
		};
	}

	@post('/unsubscribe', {
		responses: {
			'200': {
				description: 'Status',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								status: { type: 'string' }
							}
						}
					}
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async unsubscribe(
		@requestBody({
			content: {
			  'application/json': {
				schema: {
				  type: 'object',
				  properties: {
					planId: { type: 'number' }
				  },
				  required: ['planId'],
				},
			  },
			},
		  }) body: { planId: number },
		@injectUserOrgId() orgId: number,
	) {
		console.log(`unsubscribe for plan: ${body.planId} for org: ${orgId}`)
		let orgPlan = await this.orgPlanRepository.findOne({
			where: {
				orgId: orgId,
				planId: body.planId
			}
		});

		console.log(`Found org plan ${orgPlan?.id} with status: ${orgPlan?.status}, subscriptionId: ${orgPlan?.subscriptionId}`);

		if(orgPlan && orgPlan.subscriptionId != null) {
			try {
				// Check if this is a Shopify subscription (subscription ID starts with gid://shopify/)
				if (orgPlan.subscriptionId.startsWith('gid://shopify/')) {
					console.log(`Unsubscribing from Shopify subscription ${orgPlan.subscriptionId}`);

					const shopifyResult = await this.billingService.unsubscribe(
						orgId,
						orgPlan.subscriptionId
					);

					console.log(`Shopify unsubscribe result: ${JSON.stringify(shopifyResult)}`);

					if(shopifyResult.data.appSubscriptionCancel.appSubscription.status == 'CANCELLED') {
						// Clear any pending downgrades before Shopify cancellation
						console.log(`Clearing any pending downgrades for org ${orgId} before Shopify cancellation`);
						await this.orgPlanRepository.updateAll({
							status: 'CANCELLED',
							scheduledStart: undefined,
							scheduledEnd: undefined,
							subscriptionId: undefined,
							confirmationUrl: undefined
						}, {
							orgId: orgId,
							status: 'PENDING_NEXT_RENEWAL'
						});

						await this.orgPlanRepository.updateById(orgPlan.id, {
							status: 'CANCELLED',
							priceOverride: undefined,
							confirmationUrl: undefined,
							subscriptionId: undefined,
							scheduledEnd: undefined,
							scheduledStart: undefined
						});

						this.featureService.updateLoyaltyAndGwpEnabled(orgId)
							.catch((err) => console.error(err));

						return { status: 'CANCELLED' };
					}
				} else {
					// Use Stripe's cancel_at_period_end feature for cleaner cancellation handling
					console.log(`Setting cancel_at_period_end=true for Stripe subscription ${orgPlan.subscriptionId}`);

					const cancellationResult = await this.stripeBillingService.cancelSubscriptionAtPeriodEnd(
						orgId,
						orgPlan.subscriptionId
					);

					console.log(`Set subscription to cancel at period end. Period end: ${cancellationResult.periodEndDate?.toISOString()}`);

					// Set scheduledEnd to match Stripe's period end
					if (cancellationResult.periodEndDate) {
						const scheduledEndDate = cancellationResult.periodEndDate;

						// Validate the date before converting to ISO string
						if (scheduledEndDate instanceof Date && !isNaN(scheduledEndDate.getTime())) {
							// Clear any pending downgrades before setting cancellation
							console.log(`Clearing any pending downgrades for org ${orgId} before setting cancellation`);
							await this.orgPlanRepository.updateAll({
								status: 'CANCELLED',
								scheduledStart: undefined,
								scheduledEnd: undefined,
								subscriptionId: undefined,
								confirmationUrl: undefined
							}, {
								orgId: orgId,
								status: 'PENDING_NEXT_RENEWAL'
							});

							await this.orgPlanRepository.updateById(orgPlan.id, {
								scheduledEnd: scheduledEndDate.toISOString()
							});

							console.log(`Plan ${orgPlan.id} scheduled to end at period end: ${scheduledEndDate.toISOString()}`);

							return {
								status: 'SCHEDULED_CANCELLATION',
								scheduledEnd: scheduledEndDate.toISOString()
							};
						} else {
							console.error(`Invalid period end date received: ${scheduledEndDate}`);
						}
					}

					// If we don't have a valid scheduled date, cancel immediately
					console.warn(`No valid period end date, cancelling plan immediately`);

					// Clear any pending downgrades before immediate cancellation
					console.log(`Clearing any pending downgrades for org ${orgId} before immediate cancellation`);
					await this.orgPlanRepository.updateAll({
						status: 'CANCELLED',
						scheduledStart: undefined,
						scheduledEnd: undefined,
						subscriptionId: undefined,
						confirmationUrl: undefined
					}, {
						orgId: orgId,
						status: 'PENDING_NEXT_RENEWAL'
					});

					await this.orgPlanRepository.updateById(orgPlan.id, {
						status: 'CANCELLED',
						priceOverride: undefined,
						confirmationUrl: undefined,
						subscriptionId: undefined,
						scheduledEnd: undefined,
						scheduledStart: undefined
					});

					// Update features
					this.featureService.updateLoyaltyAndGwpEnabled(orgId)
						.catch((err) => console.error(err));

					return {
						status: 'CANCELLED'
					};
				}
			} catch (error) {
				console.error('Error scheduling subscription cancellation:', error);

				// Fallback to immediate cancellation if scheduling fails
				try {
					console.log(`Falling back to immediate cancellation for subscription ${orgPlan.subscriptionId}`);

					let subscription = await this.stripeBillingService.unsubscribe(
						orgId,
						orgPlan.subscriptionId
					);

					console.log(`Immediate cancellation fallback: ${JSON.stringify(subscription)}`);

					// Clear any pending downgrades before immediate cancellation fallback
					console.log(`Clearing any pending downgrades for org ${orgId} before immediate cancellation fallback`);
					await this.orgPlanRepository.updateAll({
						status: 'CANCELLED',
						scheduledStart: undefined,
						scheduledEnd: undefined,
						subscriptionId: undefined,
						confirmationUrl: undefined
					}, {
						orgId: orgId,
						status: 'PENDING_NEXT_RENEWAL'
					});

					// Mark the plan as cancelled and clear subscription info
					await this.orgPlanRepository.updateById(orgPlan.id, {
						status: 'CANCELLED',
						priceOverride: undefined,
						confirmationUrl: undefined,
						subscriptionId: undefined,
						scheduledEnd: undefined,
						scheduledStart: undefined
					});

					// Update features
					this.featureService.updateLoyaltyAndGwpEnabled(orgId)
						.catch((err) => console.error(err));

					return { status: 'CANCELLED' };
				} catch (fallbackError) {
					console.error('Even immediate cancellation fallback failed:', fallbackError);

					// Fallback to traditional cancellation if Stripe call fails
					try {
						let subscription = await this.billingService.unsubscribe(
							orgId!,
							orgPlan.subscriptionId
						);
						console.log(`Unsubscribed via legacy fallback: ${JSON.stringify(subscription)}`);

						if(subscription.data.appSubscriptionCancel.appSubscription.status == 'CANCELLED') {
							// Clear any pending downgrades before legacy cancellation
							console.log(`Clearing any pending downgrades for org ${orgId} before legacy cancellation`);
							await this.orgPlanRepository.updateAll({
								status: 'CANCELLED',
								scheduledStart: undefined,
								scheduledEnd: undefined,
								subscriptionId: undefined,
								confirmationUrl: undefined
							}, {
								orgId: orgId,
								status: 'PENDING_NEXT_RENEWAL'
							});

							await this.orgPlanRepository.updateById(orgPlan.id, {
								status: 'CANCELLED',
								priceOverride: undefined,
								confirmationUrl: undefined,
								subscriptionId: undefined,
								scheduledEnd: undefined,
								scheduledStart: undefined
							});

							this.featureService.updateLoyaltyAndGwpEnabled(orgId)
								.catch((err) => console.error(err));

							return { status: 'CANCELLED' };
						}
					} catch (legacyFallbackError) {
						console.error('All cancellation methods failed:', legacyFallbackError);

						// Clear any pending downgrades before last resort cancellation
						console.log(`Clearing any pending downgrades for org ${orgId} before last resort cancellation`);
						await this.orgPlanRepository.updateAll({
							status: 'CANCELLED',
							scheduledStart: undefined,
							scheduledEnd: undefined,
							subscriptionId: undefined,
							confirmationUrl: undefined
						}, {
							orgId: orgId,
							status: 'PENDING_NEXT_RENEWAL'
						});

						// As last resort, just update our internal records
						await this.orgPlanRepository.updateById(orgPlan.id, {
							status: 'CANCELLED',
							priceOverride: undefined,
							confirmationUrl: undefined,
							subscriptionId: undefined,
							scheduledEnd: undefined,
							scheduledStart: undefined
						});

						return { status: 'CANCELLED_LOCALLY' };
					}
				}
			}
		}
		return { status: 'FAILED' };
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/plans', {
		responses: {
			'200': {
				description: 'Array of Integration model instances',
				content: {
					'application/json': {
						schema: {
							type: 'array',
							items: getModelSchemaRef(Plan, {
								includeRelations: true,
							}),
						},
					},
				},
			},
		},
	})
	async find(
		@injectUserOrgId() orgId: number,
	): Promise<any[]> {

		const planMapping : any = {
			2: 1,
			3: 9,
			8: 9,
			4: 9,
			5: 10,
			6: 10,
			7: 11,
		};


		let plans = await this.planRepository.find({
			include: ['planRevenuePricings'],
			where: {
				active: true
			}
		});
		let orgPlans = await this.orgPlanRepository.find({
			where: {
				orgId: orgId,
			}
		});

		// **Plan Migration Logic**
		for (let orgPlan of orgPlans) {
			if (
			  orgPlan.status === 'ACTIVE' && // Only migrate if the org is on an ACTIVE plan
			  planMapping[orgPlan.planId] // Check if the current plan ID is in the mapping table (i.e., it's an old plan)
			) {
			  const newPlanId = planMapping[orgPlan.planId];
			  console.log(`Migrating org ${orgId} from plan ${orgPlan.planId} to ${newPlanId}`);

			  // Update the orgPlan record
			  await this.orgPlanRepository.updateById(orgPlan.id, {
				planId: newPlanId,
			  });

			  // Update the orgPlan object in the current loop so the change is reflected in the response
			  orgPlan.planId = newPlanId;
			}
		  }

		let finalResponse = [];
		for (let plan of plans) {
			let orgPlan = orgPlans.find((orgPlan) => {
				return orgPlan.planId == plan.id;
			});
			if (orgPlan) {
				finalResponse.push({
					...plan,
					...{
						orgId: orgId,
						status: orgPlan.status,
						action: 'current',
						priceOverride: orgPlan.priceOverride,
						subscriptionId: orgPlan.subscriptionId,
						scheduledEnd: orgPlan.scheduledEnd,
						scheduledStart: orgPlan.scheduledStart,
					}
				});
			} else {
				finalResponse.push({
					...plan,
					...{
						orgId: orgId,
						action: 'buy',
						status: 'INACTIVE',
					}
				});
			}
		}
		return finalResponse;
	}



	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/free-trial-info', {
		responses: {
			'200': {
				description: 'Get free trial info',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getFreeTrialInfo(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const org = await this.orgRepository.findById(orgId);

		// Determine which org to check for plan - use parent org if it exists
		const planOrgId = org.parentOrgId || orgId;

		const activeOrgPlan = await this.orgPlanRepository.findOne({
			where: {
				orgId: planOrgId,
				status: 'ACTIVE'
			}
		});

		const anyOrgPlan = await this.orgPlanRepository.findOne({
			where: {
				orgId: planOrgId
			}
		});

		const orgPlan = activeOrgPlan || anyOrgPlan;

		// Get current plan details
		let currentPlan = null;
		if (orgPlan?.planId) {
			currentPlan = await this.planRepository.findOne({
				where: { id: orgPlan.planId },
				include: ['planRevenuePricings']
			});
		}

		// Determine upgrade plan based on business logic
		let upgradePlan = null;
		let shouldShowUpgrade = false;

		if (orgPlan?.planId) {
			const currentPlanId = orgPlan.planId;
			let upgradePlanId = null;

			// Plan upgrade logic
			if (currentPlanId === 1) {
				// Free plan upgrades to "Strategist Lite"
				upgradePlan = await this.planRepository.findOne({
					where: { name: 'Strategist Lite' },
					include: ['planRevenuePricings']
				});
				shouldShowUpgrade = !!upgradePlan;
			} else if (currentPlanId === 13) {
				// Strategist Lite upgrades to Strategist
				upgradePlan = await this.planRepository.findOne({
					where: { name: 'Strategist' },
					include: ['planRevenuePricings']
				});
				shouldShowUpgrade = !!upgradePlan;
			} else if (currentPlanId === 14) {
				// Strategist upgrades to Strategist Max
				upgradePlan = await this.planRepository.findOne({
					where: { name: 'Strategist Max' },
					include: ['planRevenuePricings']
				});
				shouldShowUpgrade = !!upgradePlan;
			}
			// Legacy plans (2-12), Max plans (15), and Agency plans (16,17) don't show upgrade
			// currentPlanId >= 2 && currentPlanId <= 12 = legacy, no upgrade
			// currentPlanId === 15 = Max, no upgrade
			// currentPlanId === 16 || currentPlanId === 17 = Agency, no upgrade
		}

		const startDate = new Date(org!.createdDate!);
		// Agency organizations get 30 days, everyone else gets 14 days
		const trialLengthDays = org!.orgType === 'agency' ? 30 : 14;

		const date = new Date();
		const endDate = new Date(startDate);
		endDate.setDate(startDate.getDate() + trialLengthDays);

		// Format current plan details
		const currentPlanDetails = currentPlan ? {
			id: currentPlan.id,
			name: currentPlan.name,
			price: orgPlan?.priceOverride ?? currentPlan.price,
			hasRevenueBasedPricing: currentPlan.hasRevenueBasedPricing || false
		} : null;

		// Format upgrade plan details
		const upgradePlanDetails = upgradePlan ? {
			id: upgradePlan.id,
			name: upgradePlan.name,
			price: upgradePlan.price,
			hasRevenueBasedPricing: upgradePlan.hasRevenueBasedPricing || false
		} : null;

		return {
			inEffect: date < endDate,
			startDate,
			daysLeft: Math.ceil((endDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)),
			endDate,
			nextPlanId: 1,
			hasActivePlan: !!activeOrgPlan,
			hasPaidPlan: (orgPlan?.planId == 1 || orgPlan?.planId == 7 || orgPlan?.planId == 11 || orgPlan?.planId == 12 || orgPlan?.subscriptionId != null) && orgPlan?.status == 'ACTIVE',
			currentPlan: currentPlanDetails,
			upgradePlan: upgradePlanDetails,
			shouldShowUpgrade
		}
	}
}
